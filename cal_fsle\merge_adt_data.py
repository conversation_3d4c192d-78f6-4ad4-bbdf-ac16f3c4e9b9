#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADT数据合并脚本
将多个月的NRT_ADT数据文件合并成一个文件

作者: 中星海洋
日期: 2025-08-01
"""

import os
import sys
import xarray as xr
import numpy as np
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('merge_adt.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def merge_adt_files(input_dir, output_dir, file_pattern="NRT_ADT_20250*.nc", output_filename="NRT_ADT.nc"):
    """
    合并ADT数据文件
    
    参数:
        input_dir (str): 输入文件目录
        output_dir (str): 输出文件目录
        file_pattern (str): 文件匹配模式
        output_filename (str): 输出文件名
    """
    try:
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 确保输出目录存在
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找所有匹配的文件
        nc_files = sorted(list(input_path.glob(file_pattern)))
        
        if not nc_files:
            logger.error(f"在目录 {input_dir} 中未找到匹配模式 {file_pattern} 的文件")
            return False
            
        logger.info(f"找到 {len(nc_files)} 个文件待合并:")
        for file in nc_files:
            logger.info(f"  - {file.name}")
        
        # 读取所有数据集
        datasets = []
        for file_path in nc_files:
            logger.info(f"正在读取文件: {file_path.name}")
            try:
                ds = xr.open_dataset(file_path)
                
                # 检查必要的变量是否存在
                required_vars = ['adt', 'longitude', 'latitude', 'time']
                missing_vars = [var for var in required_vars if var not in ds.variables]
                if missing_vars:
                    logger.warning(f"文件 {file_path.name} 缺少变量: {missing_vars}")
                    continue
                
                datasets.append(ds)
                logger.info(f"成功读取文件: {file_path.name}, 时间维度大小: {ds.time.size}")
                
            except Exception as e:
                logger.error(f"读取文件 {file_path.name} 时出错: {str(e)}")
                continue
        
        if not datasets:
            logger.error("没有成功读取任何数据文件")
            return False
        
        logger.info("开始合并数据集...")
        
        # 沿时间维度合并数据集
        merged_ds = xr.concat(datasets, dim='time')
        
        # 按时间排序
        merged_ds = merged_ds.sortby('time')
        
        # 添加全局属性
        merged_ds.attrs.update({
            'title': 'Merged NRT ADT Data',
            'description': f'Merged from {len(datasets)} monthly files',
            'creation_date': np.datetime64('now').astype(str),
            'source_files': [f.name for f in nc_files],
            'merged_by': 'ADT Merge Script'
        })
        
        # 输出文件路径
        output_file = output_path / output_filename
        
        logger.info(f"正在保存合并后的数据到: {output_file}")
        
        # 保存合并后的数据集
        # 使用压缩以减少文件大小
        encoding = {}
        for var in merged_ds.data_vars:
            if merged_ds[var].dtype == np.float64:
                encoding[var] = {'dtype': 'float32', 'zlib': True, 'complevel': 4}
            elif merged_ds[var].dtype == np.float32:
                encoding[var] = {'zlib': True, 'complevel': 4}
        
        merged_ds.to_netcdf(output_file, encoding=encoding)
        
        # 关闭数据集释放内存
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        logger.info(f"合并完成! 输出文件: {output_file}")
        logger.info(f"合并后数据集信息:")
        
        # 重新打开文件查看信息
        final_ds = xr.open_dataset(output_file)
        logger.info(f"  - 时间范围: {final_ds.time.min().values} 到 {final_ds.time.max().values}")
        logger.info(f"  - 时间步数: {final_ds.time.size}")
        logger.info(f"  - 空间维度: longitude({final_ds.longitude.size}), latitude({final_ds.latitude.size})")
        logger.info(f"  - 文件大小: {output_file.stat().st_size / (1024*1024):.2f} MB")
        final_ds.close()
        
        return True
        
    except Exception as e:
        logger.error(f"合并过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    # 配置路径
    input_directory = r"D:\data\download"
    output_directory = r"D:\data\download"
    
    # 指定要合并的文件（2025年4-7月）
    file_pattern = "NRT_ADT_20250*.nc"
    output_filename = "NRT_ADT.nc"
    
    logger.info("开始ADT数据合并任务")
    logger.info(f"输入目录: {input_directory}")
    logger.info(f"输出目录: {output_directory}")
    logger.info(f"文件模式: {file_pattern}")
    logger.info(f"输出文件: {output_filename}")
    
    # 检查输入目录是否存在
    if not os.path.exists(input_directory):
        logger.error(f"输入目录不存在: {input_directory}")
        return
    
    # 执行合并
    success = merge_adt_files(
        input_dir=input_directory,
        output_dir=output_directory,
        file_pattern=file_pattern,
        output_filename=output_filename
    )
    
    if success:
        logger.info("ADT数据合并任务完成!")
    else:
        logger.error("ADT数据合并任务失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
