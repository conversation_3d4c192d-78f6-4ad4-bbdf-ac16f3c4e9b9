#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版ADT数据按日期拆分脚本
将NRT_ADT.nc按日期拆分为单独文件，保存到指定目录结构

作者: AI Assistant
日期: 2025-08-01
"""

import xarray as xr
import pandas as pd
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def split_adt_by_date():
    """将NRT_ADT.nc按日期拆分"""
    
    # 文件路径配置
    input_file = Path(r"D:\data\download\NRT_ADT.nc")
    output_base = Path(r"D:\data\download")
    
    logger.info(f"开始拆分文件: {input_file}")
    
    # 检查输入文件
    if not input_file.exists():
        logger.error(f"输入文件不存在: {input_file}")
        return False
    
    try:
        # 读取数据
        logger.info("正在读取数据集...")
        ds = xr.open_dataset(input_file)
        
        # 检查必要变量
        required_vars = ['adt', 'longitude', 'latitude', 'time']
        for var in required_vars:
            if var not in ds.variables:
                logger.error(f"缺少必要变量: {var}")
                return False
        
        # 获取时间信息
        times = pd.to_datetime(ds.time.values)
        logger.info(f"数据时间范围: {times.min()} 到 {times.max()}")
        logger.info(f"总共 {len(times)} 个时间点")
        
        # 按日期拆分
        success_count = 0
        for i, time_point in enumerate(times):
            try:
                # 格式化日期
                date_str = time_point.strftime('%Y%m%d')  # 20250401
                dir_name = time_point.strftime('%Y_%m_%d')  # 2025_04_01
                
                # 创建输出目录
                output_dir = output_base / dir_name
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # 输出文件名
                filename = f"nrt_global_allsat_phy_l4_{date_str}_{date_str}.nc"
                output_file = output_dir / filename
                
                # 选择当天数据
                daily_data = ds.sel(time=time_point)
                
                # 保持时间维度
                if 'time' not in daily_data.dims:
                    daily_data = daily_data.expand_dims('time')
                
                # 添加属性
                daily_data.attrs.update({
                    'title': f'NRT ADT Data - {date_str}',
                    'date': date_str,
                    'creation_date': str(pd.Timestamp.now()),
                    'variables': 'adt, longitude, latitude, time'
                })
                
                # 保存文件（使用压缩）
                encoding = {
                    'adt': {'dtype': 'float32', 'zlib': True, 'complevel': 4}
                }
                daily_data.to_netcdf(output_file, encoding=encoding)
                
                success_count += 1
                
                # 显示进度
                if (i + 1) % 20 == 0 or (i + 1) == len(times):
                    logger.info(f"进度: {i + 1}/{len(times)} - 已保存: {filename}")
                
                daily_data.close()
                
            except Exception as e:
                logger.error(f"处理 {time_point} 时出错: {str(e)}")
                continue
        
        ds.close()
        
        logger.info(f"拆分完成! 成功处理 {success_count}/{len(times)} 个文件")
        
        # 验证几个文件
        logger.info("验证生成的文件...")
        sample_dirs = list(output_base.glob("2025_*"))[:3]
        for sample_dir in sample_dirs:
            nc_files = list(sample_dir.glob("*.nc"))
            if nc_files:
                sample_file = nc_files[0]
                try:
                    test_ds = xr.open_dataset(sample_file)
                    logger.info(f"验证 {sample_file.name}: 变量 {list(test_ds.variables.keys())}")
                    test_ds.close()
                except Exception as e:
                    logger.error(f"验证文件 {sample_file} 失败: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"拆分过程出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始ADT数据按日期拆分...")
    
    success = split_adt_by_date()
    
    if success:
        print("✅ 拆分完成!")
        print("📁 文件保存格式: ./YYYY_MM_DD/nrt_global_allsat_phy_l4_YYYYMMDD_YYYYMMDD.nc")
        print("📍 保存位置: D:\\data\\download\\")
    else:
        print("❌ 拆分失败!")

if __name__ == "__main__":
    main()
