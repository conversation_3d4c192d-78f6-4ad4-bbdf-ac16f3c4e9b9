#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并指定的四个ADT数据文件
NRT_ADT_202504.nc, NRT_ADT_202505.nc, NRT_ADT_202506.nc, NRT_ADT_202507.nc

作者: AI Assistant  
日期: 2025-08-01
"""

import xarray as xr
import numpy as np
from pathlib import Path
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def merge_adt_files():
    """合并四个指定的ADT文件"""
    
    # 输入和输出路径
    input_dir = Path(r"D:\data\download")
    output_file = input_dir / "NRT_ADT.nc"
    
    # 指定的四个文件
    file_names = [
        "NRT_ADT_202504.nc",
        "NRT_ADT_202505.nc", 
        "NRT_ADT_202506.nc",
        "NRT_ADT_202507.nc"
    ]
    
    logger.info("开始合并ADT数据文件")
    logger.info(f"输入目录: {input_dir}")
    logger.info(f"输出文件: {output_file}")
    
    # 检查文件是否存在
    file_paths = []
    for filename in file_names:
        file_path = input_dir / filename
        if file_path.exists():
            file_paths.append(file_path)
            logger.info(f"找到文件: {filename}")
        else:
            logger.warning(f"文件不存在: {filename}")
    
    if not file_paths:
        logger.error("没有找到任何指定的文件")
        return False
    
    try:
        # 读取所有数据集
        datasets = []
        for file_path in file_paths:
            logger.info(f"正在读取: {file_path.name}")
            ds = xr.open_dataset(file_path)
            
            # 检查必要变量
            required_vars = ['adt', 'longitude', 'latitude', 'time']
            for var in required_vars:
                if var not in ds.variables:
                    logger.error(f"文件 {file_path.name} 缺少变量: {var}")
                    return False
            
            datasets.append(ds)
            logger.info(f"文件 {file_path.name} 读取成功, 时间点数: {ds.time.size}")
        
        # 合并数据集
        logger.info("正在合并数据集...")
        merged_ds = xr.concat(datasets, dim='time')
        
        # 按时间排序
        merged_ds = merged_ds.sortby('time')
        
        # 添加属性信息
        merged_ds.attrs.update({
            'title': 'Merged NRT ADT Data (2025-04 to 2025-07)',
            'description': 'Merged from 4 monthly ADT files',
            'source_files': [f.name for f in file_paths],
            'creation_date': str(np.datetime64('now')),
            'variables': 'adt, longitude, latitude, time'
        })
        
        # 保存文件（使用压缩）
        logger.info(f"正在保存到: {output_file}")
        encoding = {
            'adt': {'dtype': 'float32', 'zlib': True, 'complevel': 4},
            'longitude': {'dtype': 'float32'},
            'latitude': {'dtype': 'float32'},
            'time': {'dtype': 'float64'}
        }
        
        merged_ds.to_netcdf(output_file, encoding=encoding)
        
        # 显示结果信息
        logger.info("合并完成!")
        logger.info(f"时间范围: {merged_ds.time.min().values} 到 {merged_ds.time.max().values}")
        logger.info(f"总时间点数: {merged_ds.time.size}")
        logger.info(f"空间维度: longitude({merged_ds.longitude.size}) x latitude({merged_ds.latitude.size})")
        
        # 计算文件大小
        file_size_mb = output_file.stat().st_size / (1024 * 1024)
        logger.info(f"输出文件大小: {file_size_mb:.2f} MB")
        
        # 关闭数据集
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        return True
        
    except Exception as e:
        logger.error(f"合并过程中出错: {str(e)}")
        return False

def main():
    """主函数"""
    success = merge_adt_files()
    if success:
        print("✅ ADT数据合并成功!")
    else:
        print("❌ ADT数据合并失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
