# ADT数据合并工具

## 功能说明
将多个月的NRT ADT数据文件合并成一个NetCDF文件。

## 文件说明
- `merge_adt_data.py`: 通用的ADT数据合并脚本，支持文件模式匹配
- `merge_specific_adt.py`: 专门合并指定四个文件的脚本
- `requirements.txt`: Python依赖包列表

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行合并脚本
```bash
# 方法1: 使用专门的脚本合并四个指定文件
python merge_specific_adt.py

# 方法2: 使用通用脚本
python merge_adt_data.py
```

## 输入文件
- NRT_ADT_202504.nc
- NRT_ADT_202505.nc  
- NRT_ADT_202506.nc
- NRT_ADT_202507.nc

位置: `D:\data\download`

## 输出文件
- NRT_ADT.nc

位置: `D:\data\download`

## 数据变量
每个输入文件应包含以下变量:
- `adt`: 海面高度异常数据
- `longitude`: 经度
- `latitude`: 纬度  
- `time`: 时间

## 注意事项
- 脚本会自动检查文件是否存在
- 合并后的数据按时间排序
- 输出文件使用压缩以减少文件大小
- 运行过程会生成日志信息
