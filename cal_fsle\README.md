# ADT数据处理工具

## 功能说明
1. **数据合并**: 将多个月的NRT ADT数据文件合并成一个NetCDF文件
2. **数据拆分**: 将合并后的文件按日期拆分为单独的日文件

## 文件说明
### 合并功能
- `merge_adt_data.py`: 通用的ADT数据合并脚本，支持文件模式匹配
- `merge_specific_adt.py`: 专门合并指定四个文件的脚本

### 拆分功能
- `split_adt_by_date.py`: 完整版按日期拆分脚本，包含详细日志和验证
- `split_adt_simple.py`: 简化版按日期拆分脚本
- `run_split.bat`: Windows批处理脚本，方便运行拆分功能

### 其他
- `requirements.txt`: Python依赖包列表

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行脚本

#### 数据合并
```bash
# 方法1: 使用专门的脚本合并四个指定文件
python merge_specific_adt.py

# 方法2: 使用通用脚本
python merge_adt_data.py
```

#### 数据拆分
```bash
# 方法1: 使用简化版脚本（推荐）
python split_adt_simple.py

# 方法2: 使用完整版脚本
python split_adt_by_date.py

# 方法3: Windows下使用批处理
run_split.bat
```

## 输入文件
- NRT_ADT_202504.nc
- NRT_ADT_202505.nc  
- NRT_ADT_202506.nc
- NRT_ADT_202507.nc

位置: `D:\data\download`

## 输出文件

### 合并输出
- NRT_ADT.nc (位置: `D:\data\download`)

### 拆分输出
按日期分类保存的单日文件:
- `./2025_04_01/nrt_global_allsat_phy_l4_20250401_20250401.nc`
- `./2025_04_02/nrt_global_allsat_phy_l4_20250402_20250402.nc`
- `...`
- `./2025_07_31/nrt_global_allsat_phy_l4_20250731_20250731.nc`

位置: `D:\data\download\YYYY_MM_DD\`

## 数据变量
每个输入文件应包含以下变量:
- `adt`: 海面高度异常数据
- `longitude`: 经度
- `latitude`: 纬度  
- `time`: 时间

## 注意事项
- 脚本会自动检查文件是否存在
- 合并后的数据按时间排序
- 输出文件使用压缩以减少文件大小
- 运行过程会生成日志信息
