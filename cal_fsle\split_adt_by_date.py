#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将合并的NRT_ADT.nc文件按日期拆分为单独的文件
每个文件包含一天的数据，按照指定的目录结构和文件名格式保存

作者: AI Assistant
日期: 2025-08-01
"""

import xarray as xr
import numpy as np
import pandas as pd
from pathlib import Path
import logging
import sys
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('split_adt.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def split_adt_by_date(input_file, output_base_dir):
    """
    按日期拆分ADT数据文件
    
    参数:
        input_file (str): 输入的合并文件路径
        output_base_dir (str): 输出基础目录
    """
    try:
        input_path = Path(input_file)
        output_base = Path(output_base_dir)
        
        # 检查输入文件是否存在
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_file}")
            return False
        
        logger.info(f"开始处理文件: {input_path}")
        logger.info(f"输出基础目录: {output_base}")
        
        # 读取数据集
        logger.info("正在读取数据集...")
        ds = xr.open_dataset(input_path)
        
        # 检查必要变量
        required_vars = ['adt', 'longitude', 'latitude', 'time']
        missing_vars = [var for var in required_vars if var not in ds.variables]
        if missing_vars:
            logger.error(f"数据集缺少必要变量: {missing_vars}")
            return False
        
        logger.info(f"数据集信息:")
        logger.info(f"  - 时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
        logger.info(f"  - 时间点数: {ds.time.size}")
        logger.info(f"  - 空间维度: longitude({ds.longitude.size}) x latitude({ds.latitude.size})")
        
        # 获取所有时间点
        times = pd.to_datetime(ds.time.values)
        logger.info(f"开始按日期拆分，共 {len(times)} 个时间点")
        
        # 统计变量
        success_count = 0
        error_count = 0
        
        # 按每个时间点拆分
        for i, time_point in enumerate(times):
            try:
                # 格式化日期字符串
                date_str = time_point.strftime('%Y%m%d')
                dir_date_str = time_point.strftime('%Y_%m_%d')
                
                # 创建输出目录
                output_dir = output_base / dir_date_str
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # 生成输出文件名
                output_filename = f"nrt_global_allsat_phy_l4_{date_str}_{date_str}.nc"
                output_file = output_dir / output_filename
                
                # 选择当前时间点的数据
                daily_ds = ds.sel(time=time_point)
                
                # 确保时间维度保持为数组形式
                if 'time' not in daily_ds.dims:
                    daily_ds = daily_ds.expand_dims('time')
                
                # 添加文件属性
                daily_ds.attrs.update({
                    'title': f'NRT Global ADT Data - {date_str}',
                    'description': f'Daily ADT data for {time_point.strftime("%Y-%m-%d")}',
                    'date': date_str,
                    'creation_date': str(np.datetime64('now')),
                    'source': 'Split from merged NRT_ADT.nc',
                    'variables': 'adt, longitude, latitude, time'
                })
                
                # 设置编码以优化文件大小
                encoding = {
                    'adt': {'dtype': 'float32', 'zlib': True, 'complevel': 4},
                    'longitude': {'dtype': 'float32'},
                    'latitude': {'dtype': 'float32'},
                    'time': {'dtype': 'float64'}
                }
                
                # 保存文件
                daily_ds.to_netcdf(output_file, encoding=encoding)
                
                success_count += 1
                
                # 每处理10个文件输出一次进度
                if (i + 1) % 10 == 0 or (i + 1) == len(times):
                    logger.info(f"进度: {i + 1}/{len(times)} - 最新保存: {output_file}")
                
                # 关闭当前数据集
                daily_ds.close()
                
            except Exception as e:
                error_count += 1
                logger.error(f"处理时间点 {time_point} 时出错: {str(e)}")
                continue
        
        # 关闭原始数据集
        ds.close()
        
        # 输出统计信息
        logger.info(f"拆分完成!")
        logger.info(f"成功处理: {success_count} 个文件")
        logger.info(f"处理失败: {error_count} 个文件")
        logger.info(f"输出目录: {output_base}")
        
        return error_count == 0
        
    except Exception as e:
        logger.error(f"拆分过程中发生错误: {str(e)}")
        return False

def verify_split_files(output_base_dir, sample_size=5):
    """
    验证拆分后的文件
    
    参数:
        output_base_dir (str): 输出基础目录
        sample_size (int): 抽样验证的文件数量
    """
    try:
        output_base = Path(output_base_dir)
        
        # 查找所有生成的nc文件
        nc_files = list(output_base.rglob("*.nc"))
        
        if not nc_files:
            logger.warning("未找到任何生成的nc文件")
            return False
        
        logger.info(f"验证拆分结果: 找到 {len(nc_files)} 个文件")
        
        # 抽样验证
        sample_files = nc_files[:min(sample_size, len(nc_files))]
        
        for file_path in sample_files:
            try:
                ds = xr.open_dataset(file_path)
                
                # 检查变量
                required_vars = ['adt', 'longitude', 'latitude', 'time']
                has_all_vars = all(var in ds.variables for var in required_vars)
                
                logger.info(f"验证文件: {file_path.name}")
                logger.info(f"  - 包含所有必要变量: {has_all_vars}")
                logger.info(f"  - 时间点数: {ds.time.size}")
                logger.info(f"  - 文件大小: {file_path.stat().st_size / (1024*1024):.2f} MB")
                
                ds.close()
                
            except Exception as e:
                logger.error(f"验证文件 {file_path} 时出错: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"验证过程中出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 配置路径
    input_file = r"D:\data\download\NRT_ADT.nc"
    output_base_dir = r"D:\data\download"
    
    logger.info("开始ADT数据按日期拆分任务")
    logger.info(f"输入文件: {input_file}")
    logger.info(f"输出基础目录: {output_base_dir}")
    
    # 执行拆分
    success = split_adt_by_date(input_file, output_base_dir)
    
    if success:
        logger.info("拆分任务完成，开始验证...")
        verify_split_files(output_base_dir)
        logger.info("✅ ADT数据按日期拆分任务完成!")
    else:
        logger.error("❌ ADT数据拆分任务失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
